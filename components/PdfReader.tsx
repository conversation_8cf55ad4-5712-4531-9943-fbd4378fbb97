'use client'

import React, { useState, useEffect, useRef } from 'react'
import { ChevronLeft, ChevronRight, BookOpen, Settings, Bookmark, MessageCircle, Highlighter } from 'lucide-react'
import { Button } from '@/components/ui/button'
// Simplified version without complex UI components for now

interface PdfReaderProps {
  pdfUrl: string
  title?: string
  author?: string
  onClose?: () => void
}

export default function PdfReader({ pdfUrl, title = 'PDF Document', author = 'Unknown Author', onClose }: PdfReaderProps) {
  const [showSettings, setShowSettings] = useState(false)
  const [fontSize, setFontSize] = useState(16)
  const [fontFamily, setFontFamily] = useState('Georgia')
  const [theme, setTheme] = useState('light')
  const [lineHeight, setLineHeight] = useState(1.6)

  // Simple approach: use browser's native PDF viewer
  const pdfViewerUrl = `${pdfUrl}#toolbar=1&navpanes=1&scrollbar=1`

  // No complex PDF.js setup needed - use browser's native PDF viewer

  return (
    <div className={`min-h-screen ${theme === 'dark' ? 'bg-gray-900 text-white' : 'bg-white text-gray-900'}`}>
      {/* Header */}
      <div className="sticky top-0 z-50 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 px-4 py-3">
        <div className="flex items-center justify-between max-w-6xl mx-auto">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="flex items-center space-x-2"
            >
              <ChevronLeft className="h-4 w-4" />
              <span>Library</span>
            </Button>
            
            <div className="hidden md:block">
              <h1 className="font-semibold text-lg">{title}</h1>
              <p className="text-sm text-gray-600 dark:text-gray-400">{author}</p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowSettings(!showSettings)}
            >
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Settings Panel - Simplified */}
      {showSettings && (
        <div className="sticky top-16 z-40 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Theme</label>
                <select
                  value={theme}
                  onChange={(e) => setTheme(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                >
                  <option value="light">Light</option>
                  <option value="dark">Dark</option>
                  <option value="sepia">Sepia</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Font Size: {fontSize}px</label>
                <input
                  type="range"
                  min="12"
                  max="24"
                  value={fontSize}
                  onChange={(e) => setFontSize(parseInt(e.target.value))}
                  className="w-full"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Font Family</label>
                <select
                  value={fontFamily}
                  onChange={(e) => setFontFamily(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                >
                  <option value="Georgia">Georgia</option>
                  <option value="Times New Roman">Times New Roman</option>
                  <option value="Arial">Arial</option>
                  <option value="Helvetica">Helvetica</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* PDF Content - Use browser's native PDF viewer */}
      <div className="flex-1 h-screen">
        <iframe
          src={pdfViewerUrl}
          className="w-full h-full border-0"
          title={title}
          style={{ height: 'calc(100vh - 120px)' }}
        />
      </div>
    </div>
  )
}
