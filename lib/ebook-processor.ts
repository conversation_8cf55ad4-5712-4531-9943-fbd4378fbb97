// Dynamic imports to avoid build-time issues
// import pdf from 'pdf-parse'
// import * as J<PERSON><PERSON><PERSON> from 'jszip'
// import { parseString } from 'xml2js'
// import readingTime from 'reading-time'
// import { readabilityScore } from 'readability-score'
// import { WordTokenizer, SentenceTokenizer } from 'natural'

export interface Chapter {
  title: string
  content: string
  wordCount: number
  pageNumber?: number
  startPage?: number
  endPage?: number
}

export interface EbookMetadata {
  title?: string
  author?: string
  description?: string
  language?: string
  publisher?: string
  publishedDate?: string
  isbn?: string
  subjects?: string[]
  rights?: string
  format?: string
}

export interface ProcessingResult {
  chapters: Chapter[]
  wordCount: number
  pageCount: number
  readingTimeMinutes: number
  readabilityScore: number
  suggestedTags: string[]
  metadata: EbookMetadata
  description?: string
  contentAnalysis?: {
    complexity: 'Simple' | 'Moderate' | 'Complex'
    sentiment: 'Positive' | 'Neutral' | 'Negative'
    pacing: 'Fast' | 'Moderate' | 'Slow'
    dialogueRatio: number
    descriptiveRatio: number
    actionRatio: number
  }
}

// Tokenizers will be initialized dynamically

/**
 * Main function to process ebook files (PDF or EPUB)
 */
export async function processEbook(fileUrl: string, fileType: 'pdf' | 'epub'): Promise<ProcessingResult> {
  console.log(`Processing ${fileType.toUpperCase()} file:`, fileUrl)

  try {
    // Fetch the file with proper headers for Supabase
    const response = await fetch(fileUrl, {
      method: 'GET',
      headers: {
        'User-Agent': 'OnlyDiary-EbookProcessor/1.0',
      },
    })

    console.log('Fetch response:', {
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries(response.headers.entries())
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch file: ${response.status} ${response.statusText}`)
    }

    const buffer = await response.arrayBuffer()
    console.log('Buffer size:', buffer.byteLength)

    if (fileType === 'pdf') {
      return await processPDF(Buffer.from(buffer), false) // Full processing
    } else {
      return await processEPUB(Buffer.from(buffer), false) // Full processing
    }
  } catch (error) {
    console.error('Error processing ebook:', error)
    throw new Error(`Failed to process ${fileType}: ${error.message}`)
  }
}

/**
 * Convert PDF to EPUB format
 */
async function convertPDFToEPUB(pdfBuffer: Buffer, metadata: any, chapters: any[]): Promise<Buffer> {
  try {
    console.log('Converting PDF to EPUB format...')
    console.log('Metadata:', metadata)
    console.log('Chapters count:', chapters.length)

    // Validate chapters
    if (!chapters || chapters.length === 0) {
      throw new Error('No chapters provided for EPUB conversion')
    }

    // Dynamic import of epub-gen
    console.log('Importing epub-gen...')
    const Epub = (await import('epub-gen')).default
    const fs = require('fs')
    const path = require('path')
    const os = require('os')

    // Create a temporary directory for the EPUB
    console.log('Creating temporary directory...')
    const tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'epub-'))
    const epubPath = path.join(tempDir, 'converted.epub')
    console.log('Temp EPUB path:', epubPath)

    // Prepare EPUB options
    const epubOptions = {
      title: metadata.title || 'Untitled Book',
      author: metadata.author || 'Unknown Author',
      description: metadata.description || '',
      publisher: 'OnlyDiary',
      output: epubPath,
      css: `
        body {
          font-family: Georgia, serif;
          line-height: 1.6;
          margin: 0;
          padding: 20px;
        }
        h1 {
          font-size: 1.8em;
          margin-bottom: 1em;
          text-align: center;
          font-weight: bold;
        }
        p {
          margin-bottom: 1em;
          text-align: justify;
        }
        em {
          font-style: italic;
        }
        strong {
          font-weight: bold;
        }
        .chapter-title {
          font-size: 2em;
          font-weight: bold;
          text-align: center;
          margin: 2em 0;
          text-transform: uppercase;
        }
      `,
      content: chapters.map((chapter, index) => {
        console.log(`Processing chapter ${index + 1}: ${chapter.title}`)
        return {
          title: chapter.title || `Chapter ${index + 1}`,
          data: `
            <div class="chapter">
              <h1 class="chapter-title">${chapter.title || `Chapter ${index + 1}`}</h1>
              <div class="chapter-content">
                ${chapter.content || 'No content available'}
              </div>
            </div>
          `,
          excludeFromToc: false,
          beforeToc: false
        }
      })
    }

    console.log('EPUB options prepared, generating EPUB...')

    // Generate EPUB
    await new Promise<void>((resolve, reject) => {
      const epub = new Epub(epubOptions)
      epub.promise.then(() => {
        console.log('EPUB generation completed')
        resolve()
      }).catch((err) => {
        console.error('EPUB generation failed:', err)
        reject(err)
      })
    })

    // Check if file was created
    if (!fs.existsSync(epubPath)) {
      throw new Error('EPUB file was not created')
    }

    // Read the generated EPUB file
    console.log('Reading generated EPUB file...')
    const epubBuffer = fs.readFileSync(epubPath)

    // Clean up temporary files
    console.log('Cleaning up temporary files...')
    fs.unlinkSync(epubPath)
    fs.rmdirSync(tempDir)

    console.log(`Successfully converted PDF to EPUB, size: ${epubBuffer.length} bytes`)
    return epubBuffer

  } catch (error) {
    console.error('PDF to EPUB conversion failed:', error)
    throw new Error(`PDF to EPUB conversion failed: ${error.message}`)
  }
}

/**
 * Process ebook from buffer (for API endpoints)
 */
export async function processEbookBuffer(buffer: Buffer, fileType: 'pdf' | 'epub', previewOnly: boolean = false): Promise<ProcessingResult> {
  console.log(`Processing ${fileType.toUpperCase()} buffer${previewOnly ? ' (PREVIEW MODE - Chapter 1 only)' : ''}`)

  try {
    if (fileType === 'pdf') {
      try {
        // Convert PDF to EPUB, then process as EPUB
        console.log('Converting PDF to EPUB for better processing...')

        // First, extract basic content and metadata from PDF
        console.log('Extracting PDF content...')
        const pdfData = await processPDFWithPdf2Json(buffer)
        console.log('PDF data extracted, text length:', pdfData.text?.length || 0)

        // Create a simple tokenizer for chapter extraction
        const simpleTokenizer = { tokenize: (text: string) => text.split(/\s+/) }
        const chapters = extractFormattedChaptersFromPDF(pdfData.text, simpleTokenizer, previewOnly, pdfData.pageLayoutInfo)
        console.log('Chapters extracted:', chapters.length)

        // If no chapters found, create a single chapter
        if (chapters.length === 0) {
          console.log('No chapters found, creating single chapter')
          const content = pdfData.text || ''
          const limitedContent = previewOnly && content.length > 10000 ? content.substring(0, 10000) + '...' : content

          chapters.push({
            title: 'Chapter 1',
            content: convertPDFTextToHTML(limitedContent),
            chapter_number: 1,
            word_count: limitedContent.split(/\s+/).length
          })
        }

        const metadata = {
          title: pdfData.info?.Title || 'Untitled Book',
          author: pdfData.info?.Author || 'Unknown Author',
          description: pdfData.info?.Subject || ''
        }

        console.log('Converting to EPUB with', chapters.length, 'chapters')

        // Convert to EPUB
        const epubBuffer = await convertPDFToEPUB(buffer, metadata, chapters)
        console.log('EPUB conversion successful, processing as EPUB...')

        // Process the generated EPUB through the existing EPUB pipeline
        return await processEPUB(epubBuffer, previewOnly)

      } catch (pdfError) {
        console.error('PDF to EPUB conversion failed, falling back to direct PDF processing:', pdfError)
        // Fallback to direct PDF processing if conversion fails
        return await processPDF(buffer, previewOnly)
      }
    } else {
      return await processEPUB(buffer, previewOnly)
    }
  } catch (error) {
    console.error('Error processing ebook buffer:', error)
    throw error
  }
}

/**
 * Detect if text is likely a page number, header, or footer
 */
function isPageNumberOrHeader(text: string, textItem: any): boolean {
  const trimmed = text.trim()

  // Skip empty or very short text
  if (trimmed.length === 0 || trimmed.length > 50) {
    return false
  }

  // Check if it's just a number (page number)
  if (/^\d+$/.test(trimmed)) {
    return true
  }

  // Check if it's positioned like a header/footer (very top or bottom of page)
  if (textItem.y && (textItem.y < 2 || textItem.y > 36)) {
    return true
  }

  // Check for common header/footer patterns
  if (/^(page|chapter|\d+|\|)/i.test(trimmed)) {
    return true
  }

  return false
}

/**
 * Fallback PDF processing using pdf2json when pdf-parse fails
 */
async function processPDFWithPdf2Json(buffer: Buffer): Promise<any> {
  const PDFParser = (await import('pdf2json')).default

  return new Promise((resolve, reject) => {
    const pdfParser = new PDFParser()

    pdfParser.on('pdfParser_dataError', (errData: any) => {
      reject(new Error(`pdf2json error: ${errData.parserError}`))
    })

    pdfParser.on('pdfParser_dataReady', (pdfData: any) => {
      try {
        // Extract text from pdf2json format - optimized for speed
        let fullText = ''
        let pageCount = 0
        const pageLayoutInfo = [] // Store layout info for visual chapter detection

        // Try different possible data structures
        const pages = pdfData.Pages || pdfData.formImage?.Pages || []
        pageCount = pages.length

        console.log(`Found ${pageCount} pages in PDF`)

        // For preview mode, only process first few pages to speed things up
        const pagesToProcess = pages.length

        for (let pageIndex = 0; pageIndex < Math.min(pagesToProcess, pages.length); pageIndex++) {
          const page = pages[pageIndex]

          if (page.Texts && Array.isArray(page.Texts)) {
            // Extract text with formatting preservation and collect layout info
            const pageTextItems = []

            for (const textItem of page.Texts) {
              if (textItem.R && Array.isArray(textItem.R)) {
                for (const run of textItem.R) {
                  if (run.T) {
                    try {
                      // Decode URI component
                      let decodedText = decodeURIComponent(run.T)

                      // Skip page numbers and headers/footers
                      if (isPageNumberOrHeader(decodedText, textItem)) {
                        continue
                      }

                      // Collect layout information for chapter detection
                      const fontSize = run.TS && run.TS[1] ? run.TS[1] : 12
                      const yPosition = textItem.y || 0

                      // Check for formatting - the 4th element in TS array indicates style
                      if (run.TS && run.TS.length >= 4) {
                        const styleFlag = run.TS[3]

                        // Just handle italic text - no special drop cap handling
                        if (styleFlag === 1) {
                          decodedText = `<em>${decodedText}</em>`
                        }
                      }

                      // Store text with layout info for chapter detection
                      pageTextItems.push({
                        text: decodedText,
                        fontSize,
                        yPosition,
                        pageIndex: pageIndex
                      })

                      fullText += decodedText + ' '
                    } catch (decodeError) {
                      // If decoding fails, use raw text
                      fullText += run.T + ' '
                    }
                  }
                }
              }
            }

            // Store page layout info for chapter detection
            pageLayoutInfo.push(pageTextItems)
            fullText += '\n\n' // Add paragraph breaks between pages
          }
        }

        console.log(`Extracted text length: ${fullText.length}`)

        // Return data in pdf-parse compatible format
        resolve({
          text: fullText.trim(),
          numpages: pageCount,
          info: pdfData.Meta || {},
          pageLayoutInfo: pageLayoutInfo // Pass layout info for chapter detection
        })
      } catch (error) {
        console.error('Error processing pdf2json data:', error)
        reject(new Error(`Failed to process pdf2json data: ${error.message}`))
      }
    })

    // Parse the buffer
    pdfParser.parseBuffer(buffer)
  })
}



/**
 * Process PDF files (now converts to EPUB for better e-reader support)
 */
async function processPDF(buffer: Buffer, previewOnly: boolean = false): Promise<ProcessingResult> {
  try {
    const { WordTokenizer, SentenceTokenizer } = await import('natural')
    const readingTime = (await import('reading-time')).default

    const wordTokenizer = new WordTokenizer()
    const sentenceTokenizer = new SentenceTokenizer(['Dr.', 'Mr.', 'Mrs.', 'Ms.', 'Prof.', 'Sr.', 'Jr.'])

    console.log('Processing PDF buffer, size:', buffer.length)

    let data

    // Try pdf2json first since pdf-parse has import issues
    try {
      console.log('Using pdf2json for PDF processing...')
      data = await processPDFWithPdf2Json(buffer)
      console.log('Successfully processed PDF with pdf2json')
    } catch (pdf2jsonError) {
      console.error('pdf2json failed, trying pdf-parse fallback:', pdf2jsonError)

      // Fallback to pdf-parse if pdf2json fails
      try {
        const pdf = (await import('pdf-parse')).default
        data = await pdf(buffer)
        console.log('Successfully processed PDF with pdf-parse fallback')
      } catch (pdfParseError) {
        console.error('Both pdf2json and pdf-parse failed:', pdfParseError)
        throw new Error(`PDF processing failed: ${pdf2jsonError.message}`)
      }
    }

    console.log('PDF parsing successful:', {
      textLength: data.text?.length || 0,
      pageCount: data.numpages || 0,
      hasInfo: !!data.info
    })

    const fullText = data.text || ''
    const pageCount = data.numpages || 0

    if (!fullText || fullText.trim().length === 0) {
      throw new Error('No text content could be extracted from the PDF')
    }

    // Extract chapters from PDF with formatting preservation
    const chapters = extractFormattedChaptersFromPDF(fullText, wordTokenizer, previewOnly, data.pageLayoutInfo)

    // Calculate metrics
    const wordCount = wordTokenizer.tokenize(fullText)?.length || 0
    const readingStats = readingTime(fullText)

    const suggestedTags = extractKeywords(fullText, [], wordTokenizer, sentenceTokenizer)
    
    // Extract basic metadata (PDFs have limited metadata)
    const metadata: EbookMetadata = {
      title: data.info?.Title || undefined,
      author: data.info?.Author || undefined,
      description: data.info?.Subject || undefined
    }

    return {
      chapters,
      wordCount,
      pageCount: pageCount, // Use actual PDF page count, not estimated
      readingTimeMinutes: Math.ceil(readingStats.minutes),
      readabilityScore: 0, // Removed - not needed for diary/book platform
      suggestedTags,
      metadata,
      description: generateDescription(fullText, sentenceTokenizer),
      contentAnalysis: analyzeContentQuality(fullText, wordTokenizer, sentenceTokenizer)
    }
  } catch (error) {
    console.error('PDF processing error:', error)
    throw new Error(`PDF processing failed: ${error.message}`)
  }
}

/**
 * Process EPUB files
 */
async function processEPUB(buffer: Buffer, previewOnly: boolean = false): Promise<ProcessingResult> {
  try {
    // Dynamic imports to avoid build-time issues
    const JSZip = (await import('jszip')).default
    const { parseString } = await import('xml2js')
    const { WordTokenizer, SentenceTokenizer } = await import('natural')
    const readingTime = (await import('reading-time')).default


    const wordTokenizer = new WordTokenizer()
    const sentenceTokenizer = new SentenceTokenizer(['Dr.', 'Mr.', 'Mrs.', 'Ms.', 'Prof.', 'Sr.', 'Jr.'])

    // Local XML parsing function
    const parseXmlAsync = (xml: string): Promise<any> => {
      return new Promise((resolve, reject) => {
        parseString(xml, (err, result) => {
          if (err) reject(err)
          else resolve(result)
        })
      })
    }

    // Parse EPUB as ZIP file
    const zip = await JSZip.loadAsync(buffer)

    // Extract metadata from OPF file
    let metadata: EbookMetadata = {
      title: 'Unknown Title',
      author: 'Unknown Author',
      description: '',
      language: 'en',
      publisher: '',
      publishedDate: '',
      isbn: '',
      subjects: [],
      rights: '',
      format: 'epub'
    }

    // Find and parse the OPF file for metadata
    const containerFile = zip.file('META-INF/container.xml')
    if (containerFile) {
      const containerXml = await containerFile.async('text')
      const containerData = await parseXmlAsync(containerXml)

      // Get the OPF file path
      const opfPath = containerData?.container?.rootfiles?.[0]?.rootfile?.[0]?.$?.['full-path']
      if (opfPath) {
        const opfFile = zip.file(opfPath)
        if (opfFile) {
          const opfXml = await opfFile.async('text')
          const opfData = await parseXmlAsync(opfXml)

          // Extract metadata from OPF
          const opfMetadata = opfData?.package?.metadata?.[0]
          if (opfMetadata) {
            metadata.title = extractTextFromXmlElement(opfMetadata['dc:title']) || metadata.title
            metadata.author = extractTextFromXmlElement(opfMetadata['dc:creator']) || metadata.author
            metadata.description = extractTextFromXmlElement(opfMetadata['dc:description']) || metadata.description
            metadata.language = extractTextFromXmlElement(opfMetadata['dc:language']) || metadata.language
            metadata.publisher = extractTextFromXmlElement(opfMetadata['dc:publisher']) || metadata.publisher
            metadata.publishedDate = extractTextFromXmlElement(opfMetadata['dc:date']) || metadata.publishedDate
            metadata.isbn = extractTextFromXmlElement(opfMetadata['dc:identifier']) || metadata.isbn

            const subjects = opfMetadata['dc:subject']
            if (subjects) {
              metadata.subjects = Array.isArray(subjects)
                ? subjects.map(s => extractTextFromXmlElement(s)).filter(Boolean)
                : [extractTextFromXmlElement(subjects)].filter(Boolean)
            }
          }
        }
      }
    }

    const chapters: Chapter[] = []
    let fullText = ''
    let chapterNumber = 1

    // Find HTML/XHTML files in the EPUB
    const htmlFiles = Object.keys(zip.files).filter(filename =>
      filename.match(/\.(x?html?)$/i) && !filename.startsWith('META-INF/')
    )

    console.log(`Found ${htmlFiles.length} HTML files in EPUB:`, htmlFiles)

    // Sort files properly by chapter number
    const sortedHtmlFiles = htmlFiles.sort((a, b) => {
      const aMatch = a.match(/chapter-(\d+)/i)
      const bMatch = b.match(/chapter-(\d+)/i)

      if (aMatch && bMatch) {
        return parseInt(aMatch[1]) - parseInt(bMatch[1])
      }

      // Put non-chapter files at the end
      if (aMatch && !bMatch) return -1
      if (!aMatch && bMatch) return 1

      return a.localeCompare(b)
    })

    console.log('Sorted HTML files for processing:', sortedHtmlFiles)

    // Process each HTML file as a potential chapter (in correct order)
    for (const filename of sortedHtmlFiles) {
      try {
        const file = zip.file(filename)
        if (file) {
          const htmlContent = await file.async('text')
          // Extract title first
          const titleMatch = htmlContent.match(/<title[^>]*>([^<]+)<\/title>/i)
          const h1Match = htmlContent.match(/<h1[^>]*>([^<]+)<\/h1>/i)
          const h2Match = htmlContent.match(/<h2[^>]*>([^<]+)<\/h2>/i)
          const chapterTitle = titleMatch?.[1] || h1Match?.[1] || h2Match?.[1] || `Chapter ${chapterNumber}`

          const cleanText = preserveEpubFormatting(htmlContent, chapterTitle)

          console.log(`Processing ${filename}:`, {
            htmlLength: htmlContent.length,
            cleanTextLength: cleanText.length,
            cleanTextPreview: cleanText.substring(0, 200)
          })

          if (cleanText.trim().length > 100) { // Only include substantial content

            const wordCount = wordTokenizer.tokenize(cleanText)?.length || 0

            const chapter = {
              title: chapterTitle.trim(),
              content: cleanText,
              wordCount: wordCount
            }

            console.log(`Created chapter ${chapterNumber}:`, {
              title: chapter.title,
              contentLength: chapter.content.length,
              wordCount: chapter.wordCount,
              contentStart: chapter.content.substring(0, 100)
            })

            chapters.push(chapter)
            fullText += cleanText + '\n\n'
            chapterNumber++

            // If preview mode, stop after first chapter
            if (previewOnly && chapters.length >= 1) {
              console.log('PREVIEW MODE: Stopping after first chapter')
              break
            }
          } else {
            console.log(`Skipping ${filename} - content too short (${cleanText.length} chars)`)
          }
        }
      } catch (error) {
        console.error(`Error processing file ${filename}:`, error)
        continue
      }
    }

    console.log(`Total chapters extracted: ${chapters.length}`)

    // If no chapters found, create a single chapter from all content
    if (chapters.length === 0) {
      chapters.push({
        title: 'Content',
        content: 'No readable content found in this EPUB file.',
        chapter_number: 1,
        word_count: 0
      })
    }

    const wordCount = wordTokenizer.tokenize(fullText)?.length || 0
    const readingStats = readingTime(fullText || 'No content')

    const suggestedTags = extractKeywords(fullText, metadata.subjects, wordTokenizer, sentenceTokenizer)

    return {
      chapters,
      wordCount,
      pageCount: Math.ceil(wordCount / 250), // Estimate pages (250 words per page)
      readingTimeMinutes: Math.ceil(readingStats.minutes),
      readabilityScore: 0, // Removed - not needed for diary/book platform
      suggestedTags,
      metadata,
      description: metadata.description || generateDescription(fullText, sentenceTokenizer),
      contentAnalysis: analyzeContentQuality(fullText, wordTokenizer, sentenceTokenizer)
    }
  } catch (error) {
    console.error('EPUB processing error:', error)
    throw new Error(`EPUB processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

/**
 * Parse XML string asynchronously
 */
function parseXmlAsync(xml: string): Promise<any> {
  return new Promise((resolve, reject) => {
    parseString(xml, (err, result) => {
      if (err) reject(err)
      else resolve(result)
    })
  })
}

/**
 * Extract text content from XML element
 */
function extractTextFromXmlElement(element: any): string {
  if (!element) return ''
  if (typeof element === 'string') return element
  if (Array.isArray(element) && element.length > 0) {
    const first = element[0]
    if (typeof first === 'string') return first
    if (first && typeof first === 'object' && first._) return first._
    if (first && typeof first === 'object' && first.$text) return first.$text
  }
  if (typeof element === 'object' && element._) return element._
  if (typeof element === 'object' && element.$text) return element.$text
  return ''
}

/**
 * Universal chapter detection that works for any PDF structure
 */
function findUniversalChapterTitles(text: string): Array<{title: string, startIndex: number}> {
  const chapterTitles = []

  // Pattern 1: Look for "CHAPTER X" followed by title
  const traditionalChapters = Array.from(text.matchAll(/CHAPTER\s+(\d+)\s*([A-Z\s]*)/gi))
  for (const match of traditionalChapters) {
    const chapterNum = match[1]
    const title = match[2] ? match[2].trim() : `Chapter ${chapterNum}`
    chapterTitles.push({
      title: title || `Chapter ${chapterNum}`,
      startIndex: match.index!
    })
  }

  // Pattern 2: Look for character-based chapters (like GRACE, OG, BLACK BARBEE)
  if (chapterTitles.length === 0) {
    // Look for standalone all-caps titles that are likely chapter names
    const characterChapters = Array.from(text.matchAll(/\b([A-Z]{2,}(?:\s+[A-Z]{2,})*)\s+<em>/g))

    const validChapterTitles = []
    const commonWords = ['THE', 'AND', 'BUT', 'FOR', 'WITH', 'FROM', 'THAT', 'THIS', 'THEY', 'WERE', 'HAVE', 'BEEN', 'WILL', 'WOULD', 'COULD', 'SHOULD']

    for (const match of characterChapters) {
      const title = match[1].trim()

      // Filter for likely chapter titles
      if (title.length >= 2 && title.length <= 20 &&
          !commonWords.includes(title) &&
          !title.includes('CHAPTER') &&
          !title.includes('COLOR') &&
          !title.includes('WEAVER') &&
          !title.includes('PRESENTS')) {

        // Avoid duplicates
        if (!validChapterTitles.some(existing => existing.title === title)) {
          validChapterTitles.push({
            title: title,
            startIndex: match.index!
          })
        }
      }
    }

    // Sort by position and take the most likely ones
    validChapterTitles.sort((a, b) => a.startIndex - b.startIndex)
    chapterTitles.push(...validChapterTitles.slice(0, 10)) // Max 10 chapters
  }

  return chapterTitles
}

/**
 * Remove chapter headers and book metadata from content
 */
function removeChapterHeaders(content: string, chapterTitle: string): string {
  return content
    .replace(/^BROKEN CRAYONS STILL COLOR 2?\s*/i, '')
    .replace(/^DAVID WEAVER\s*/i, '')
    .replace(/^David Weaver Presents\s*/i, '')
    .replace(/^CHAPTER\s+\d+\s*/i, '')
    .replace(new RegExp(`^${chapterTitle}\\s*`, 'i'), '')
    .replace(/^M\s+GRACE\s*/i, '')
    .replace(/^M\s+/i, '') // Remove standalone "M"
    .trim()
}

/**
 * Extract chapters from PDF text - Universal approach for any PDF structure
 */
function extractFormattedChaptersFromPDF(text: string, wordTokenizer: any, previewOnly: boolean = false, pageLayoutInfo?: any[]): Chapter[] {
  const chapters: Chapter[] = []

  console.log('Extracting chapters from text, length:', text.length)

  // Simplified approach: For preview, just create Chapter 1 with smart ending detection
  if (previewOnly) {
    console.log('Preview mode: Creating Chapter 1 with smart boundary detection')

    // Find where Chapter 1 should end by looking for the next likely chapter title
    let chapter1EndIndex = text.length

    // Look for the next chapter after at least 5000 characters
    const minChapterLength = 5000
    if (text.length > minChapterLength) {
      // Look for patterns that indicate a new chapter:
      // 1. Large gaps in text (multiple line breaks)
      // 2. All caps words that could be character names
      // 3. Significant formatting changes

      const textAfterMin = text.substring(minChapterLength)

      // Look for potential chapter breaks: multiple line breaks followed by caps
      const chapterBreakPattern = /\n\s*\n\s*([A-Z]{4,15})\s+<em>/g
      const match = chapterBreakPattern.exec(textAfterMin)

      if (match) {
        const potentialTitle = match[1]
        // Skip common words
        const commonWords = ['THE', 'AND', 'BUT', 'FOR', 'WITH', 'FROM', 'THAT', 'THIS', 'THEY', 'WERE', 'HAVE', 'BEEN', 'WILL', 'WOULD']
        if (!commonWords.includes(potentialTitle)) {
          chapter1EndIndex = minChapterLength + match.index
          console.log(`Found potential Chapter 2 start: "${potentialTitle}" at position ${chapter1EndIndex}`)
        }
      }
    }

    // Extract Chapter 1 content
    let chapter1Content = text.substring(0, chapter1EndIndex).trim()

    // Limit for preview performance
    if (chapter1Content.length > 10000) {
      chapter1Content = chapter1Content.substring(0, 10000) + '...'
    }

    // Clean up the content
    chapter1Content = cleanChapterBoundaries(chapter1Content)

    // Remove title/header information from the beginning
    chapter1Content = chapter1Content
      .replace(/^BROKEN CRAYONS STILL COLOR 2?\s*/i, '')
      .replace(/^DAVID WEAVER\s*/i, '')
      .replace(/^David Weaver Presents\s*/i, '')
      .replace(/^M GRACE\s*/i, '')
      .replace(/^GRACE\s*/i, '')
      .trim()

    if (chapter1Content.length > 100) {
      const formattedContent = convertPDFTextToHTML(chapter1Content)

      chapters.push({
        title: 'GRACE',
        content: formattedContent,
        chapter_number: 1,
        word_count: wordTokenizer.tokenize(chapter1Content.toLowerCase())?.length || 0
      })

      console.log(`Preview Chapter 1 created with ${chapter1Content.length} characters`)
    }

    return chapters
  }

  // For full processing, try layout-aware detection
  console.log('Full processing mode: Using layout-aware chapter detection...')

  let chapterCandidates = []
  if (pageLayoutInfo && pageLayoutInfo.length > 0) {
    chapterCandidates = detectLayoutAwareChapters(pageLayoutInfo)
  }

  console.log(`Found ${chapterCandidates.length} chapter candidates`)

  if (chapterCandidates.length > 0) {
    for (let i = 0; i < chapterCandidates.length; i++) {
      const candidate = chapterCandidates[i]
      const nextCandidate = chapterCandidates[i + 1]

      // Calculate text boundaries for this chapter
      let chapterStartIndex = 0
      let chapterEndIndex = text.length

      // Find start position in text
      const titleInText = text.indexOf(candidate.title)
      if (titleInText >= 0) {
        chapterStartIndex = titleInText
      }

      // Find end position (start of next chapter)
      if (nextCandidate) {
        const nextTitleInText = text.indexOf(nextCandidate.title, chapterStartIndex + 100)
        if (nextTitleInText >= 0) {
          chapterEndIndex = nextTitleInText
        }
      }

      // Extract chapter content
      let chapterContent = text.substring(chapterStartIndex, chapterEndIndex).trim()

      // Clean up the content
      chapterContent = cleanChapterBoundaries(chapterContent)
      chapterContent = chapterContent.replace(new RegExp(`^${candidate.title}\\s*`, 'i'), '').trim()

      if (chapterContent.length > 100) {
        const formattedContent = convertPDFTextToHTML(chapterContent)

        chapters.push({
          title: candidate.title,
          content: formattedContent,
          chapter_number: i + 1,
          word_count: wordTokenizer.tokenize(chapterContent.toLowerCase())?.length || 0
        })

        console.log(`Chapter ${i + 1}: "${candidate.title}", content length: ${chapterContent.length}`)
      }
    }
  }

  // If no chapters found with layout detection, create a single chapter
  if (chapters.length === 0) {
    console.log('No chapter markers found, creating single chapter')
    let content = text

    // For preview, limit content to speed up processing
    if (previewOnly && content.length > 15000) {
      content = content.substring(0, 15000) + '...'
    }

    const formattedContent = convertPDFTextToHTML(content)
    chapters.push({
      title: 'Book Content',
      content: formattedContent,
      chapter_number: 1,
      word_count: wordTokenizer.tokenize(content.toLowerCase())?.length || 0
    })
  }

  console.log(`Final result: ${chapters.length} chapters created`)
  return chapters
}

/**
 * Extract chapters from plain text using common patterns (legacy function)
 */
function extractChaptersFromText(text: string, wordTokenizer: any): Chapter[] {
  const chapters: Chapter[] = []

  // Common chapter patterns
  const chapterPatterns = [
    /^Chapter\s+(\d+|[IVXLCDM]+)[\s\-:]*(.*)$/gmi,
    /^(\d+)[\.\s]+(.*)$/gm,
    /^Part\s+(\d+|[IVXLCDM]+)[\s\-:]*(.*)$/gmi,
    /^Section\s+(\d+)[\s\-:]*(.*)$/gmi
  ]

  let bestSplit = null
  let maxChapters = 0

  // Try each pattern and use the one that finds the most chapters
  for (const pattern of chapterPatterns) {
    const matches = Array.from(text.matchAll(pattern))
    if (matches.length > maxChapters) {
      maxChapters = matches.length
      bestSplit = matches
    }
  }

  if (bestSplit && bestSplit.length > 1) {
    // Split text by chapter markers
    for (let i = 0; i < bestSplit.length; i++) {
      const match = bestSplit[i]
      const nextMatch = bestSplit[i + 1]

      const startIndex = match.index!
      const endIndex = nextMatch ? nextMatch.index! : text.length

      const chapterContent = text.slice(startIndex, endIndex).trim()
      const title = match[2]?.trim() || `Chapter ${i + 1}`

      if (chapterContent.length > 200) { // Only include substantial chapters
        chapters.push({
          title,
          content: chapterContent,
          chapter_number: chapters.length + 1,
          word_count: wordTokenizer.tokenize(chapterContent.toLowerCase())?.length || 0
        })
      }
    }
  } else {
    // If no clear chapter structure, create a single chapter
    chapters.push({
      title: 'Full Text',
      content: text,
      chapter_number: 1,
      word_count: wordTokenizer.tokenize(text.toLowerCase())?.length || 0
    })
  }

  return chapters
}

/**
 * Clean HTML text and extract readable content
 */
function cleanHtmlText(html: string): string {
  // Enhanced HTML processing to preserve formatting
  let text = html
    // Preserve headings
    .replace(/<h1[^>]*>(.*?)<\/h1>/gi, '\n\n# $1\n\n')
    .replace(/<h2[^>]*>(.*?)<\/h2>/gi, '\n\n## $1\n\n')
    .replace(/<h3[^>]*>(.*?)<\/h3>/gi, '\n\n### $1\n\n')
    .replace(/<h4[^>]*>(.*?)<\/h4>/gi, '\n\n#### $1\n\n')
    .replace(/<h5[^>]*>(.*?)<\/h5>/gi, '\n\n##### $1\n\n')
    .replace(/<h6[^>]*>(.*?)<\/h6>/gi, '\n\n###### $1\n\n')

    // Preserve emphasis
    .replace(/<strong[^>]*>(.*?)<\/strong>/gi, '**$1**')
    .replace(/<b[^>]*>(.*?)<\/b>/gi, '**$1**')
    .replace(/<em[^>]*>(.*?)<\/em>/gi, '*$1*')
    .replace(/<i[^>]*>(.*?)<\/i>/gi, '*$1*')

    // Preserve line breaks and paragraphs
    .replace(/<br\s*\/?>/gi, '\n')
    .replace(/<\/p>/gi, '\n\n')
    .replace(/<\/div>/gi, '\n')

    // Preserve lists
    .replace(/<li[^>]*>(.*?)<\/li>/gi, '• $1\n')
    .replace(/<\/ul>/gi, '\n')
    .replace(/<\/ol>/gi, '\n')

    // Preserve blockquotes
    .replace(/<blockquote[^>]*>(.*?)<\/blockquote>/gi, '\n> $1\n\n')

    // Remove remaining HTML tags but preserve content
    .replace(/<[^>]*>/g, '')

  // Decode HTML entities
  text = text
    .replace(/&nbsp;/g, ' ')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")
    .replace(/&apos;/g, "'")
    .replace(/&mdash;/g, '—')
    .replace(/&ndash;/g, '–')
    .replace(/&hellip;/g, '…')
    .replace(/&ldquo;/g, '"')
    .replace(/&rdquo;/g, '"')
    .replace(/&lsquo;/g, "'")
    .replace(/&rsquo;/g, "'")

  // Clean up whitespace while preserving intentional formatting
  text = text
    .replace(/[ \t]+/g, ' ') // Multiple spaces/tabs to single space
    .replace(/\n[ \t]+/g, '\n') // Remove spaces at start of lines
    .replace(/[ \t]+\n/g, '\n') // Remove spaces at end of lines
    .replace(/\n{4,}/g, '\n\n\n') // Limit to triple line breaks max
    .trim()

  return text
}

/**
 * Two-pass layout-aware chapter detection (following the suggested approach)
 */
function detectLayoutAwareChapters(pageLayoutInfo: any[]): any[] {
  console.log('Starting layout-aware chapter detection...')

  // PASS 1: Profile the document's typography
  const fontSizes = []
  const allTextItems = []

  for (const pageItems of pageLayoutInfo) {
    for (const item of pageItems) {
      if (item.text.trim().length > 0) {
        fontSizes.push(item.fontSize)
        allTextItems.push(item)
      }
    }
  }

  // Find dominant body text font size
  const sortedSizes = fontSizes.sort((a, b) => a - b)
  const bodyTextSize = sortedSizes[Math.floor(sortedSizes.length * 0.6)] || 12 // 60th percentile
  const titleSizeThreshold = bodyTextSize * 1.5 // 150% larger than body text

  console.log(`Body text size: ${bodyTextSize}, Title threshold: ${titleSizeThreshold}`)

  // PASS 2: Apply multi-factor heuristic for chapter titles
  const chapterCandidates = []

  for (let pageIndex = 0; pageIndex < pageLayoutInfo.length; pageIndex++) {
    const pageItems = pageLayoutInfo[pageIndex]
    const pageHeight = 38.25 // Standard PDF page height in pdf2json units
    const topMargin = pageHeight * 0.25 // Top 25% of page

    for (let itemIndex = 0; itemIndex < pageItems.length; itemIndex++) {
      const item = pageItems[itemIndex]

      // Multi-factor heuristic scoring
      let score = 0
      const reasons = []

      // Factor 1: Anomalous font size
      if (item.fontSize > titleSizeThreshold) {
        score += 3
        reasons.push(`Large font (${item.fontSize} > ${titleSizeThreshold})`)
      }

      // Factor 2: All caps and reasonable length
      if (/^[A-Z\s]+$/.test(item.text.trim()) &&
          item.text.trim().length >= 3 &&
          item.text.trim().length <= 15) {
        score += 2
        reasons.push('All caps, title length')
      }

      // Factor 3: Top-of-page positioning
      if (item.yPosition < topMargin) {
        score += 2
        reasons.push('Top of page')
      }

      // Factor 4: First item on page (new page start)
      if (itemIndex === 0 || itemIndex === 1) {
        score += 1
        reasons.push('First item on page')
      }

      // Factor 5: Followed by significant white space
      const nextItem = pageItems[itemIndex + 1]
      if (nextItem && (nextItem.yPosition - item.yPosition) > 2) {
        score += 1
        reasons.push('Followed by white space')
      }

      // Factor 6: Exclude common words
      const commonWords = ['THE', 'AND', 'BUT', 'FOR', 'WITH', 'FROM', 'THAT', 'THIS', 'THEY', 'WERE', 'HAVE', 'BEEN', 'WILL', 'WOULD', 'COULD', 'SHOULD']
      if (!commonWords.includes(item.text.trim().toUpperCase())) {
        score += 1
        reasons.push('Not common word')
      }

      // If score is high enough, it's likely a chapter title
      if (score >= 5) {
        chapterCandidates.push({
          title: item.text.trim(),
          pageIndex: pageIndex,
          itemIndex: itemIndex,
          score: score,
          reasons: reasons,
          fontSize: item.fontSize,
          yPosition: item.yPosition
        })

        console.log(`Chapter candidate: "${item.text.trim()}" (score: ${score}) - ${reasons.join(', ')}`)
      }
    }
  }

  // Sort by page order and return top candidates
  return chapterCandidates
    .sort((a, b) => a.pageIndex - b.pageIndex || a.itemIndex - b.itemIndex)
    .filter((candidate, index, array) => {
      // Remove duplicates and low-quality candidates
      if (index === 0) return true // Always keep first
      const prev = array[index - 1]
      return candidate.pageIndex !== prev.pageIndex || candidate.title !== prev.title
    })
}

/**
 * Clean chapter boundaries - remove page numbers and artifacts
 */
function cleanChapterBoundaries(text: string): string {
  let cleaned = text

  // Remove standalone page numbers (digits surrounded by spaces or at line boundaries)
  cleaned = cleaned.replace(/\s+\d+\s+/g, ' ')
  cleaned = cleaned.replace(/^\d+\s+/g, '')
  cleaned = cleaned.replace(/\s+\d+$/g, '')

  // Remove common page artifacts
  cleaned = cleaned.replace(/\s*\|\s*/g, ' ') // Remove pipe characters
  cleaned = cleaned.replace(/\s*—\s*\d+\s*—\s*/g, ' ') // Remove page markers like "— 6 —"

  // Clean up excessive whitespace
  cleaned = cleaned.replace(/\s+/g, ' ').trim()

  return cleaned
}

/**
 * Universal PDF text reconstruction - fixes common extraction issues for any book
 */
function reconstructPDFText(text: string): string {
  let reconstructed = text

  // 1. First, fix the specific "any people" -> "Many people" issue and combine with "M"
  // Handle all the different patterns we've seen
  reconstructed = reconstructed.replace(/M GRACE <em>any <\/em>/i, 'M GRACE <em>Many </em>')
  reconstructed = reconstructed.replace(/^<em>any <\/em>/i, '<em>Many </em>')
  reconstructed = reconstructed.replace(/\bM\s+<em>any <\/em>/i, '<em>Many </em>')
  reconstructed = reconstructed.replace(/GRACE\s+<em>any <\/em>/i, 'GRACE <em>Many </em>')

  // More general pattern: any time we see "M" followed by space and "<em>any"
  reconstructed = reconstructed.replace(/\bM\s+GRACE\s+<em>any <\/em>/i, 'M GRACE <em>Many </em>')

  // 2. Fix broken ligatures and common spacing issues
  reconstructed = reconstructed
    .replace(/fl (?=[a-z])/g, 'fl')  // fl owed -> flowed
    .replace(/fi (?=[a-z])/g, 'fi')  // fi nal -> final
    .replace(/ff (?=[a-z])/g, 'ff')  // ff ect -> ffect
    .replace(/ft (?=[a-z])/g, 'ft')  // ft er -> fter
    .replace(/ffi (?=[a-z])/g, 'ffi') // ffi ce -> ffice
    .replace(/ffl (?=[a-z])/g, 'ffl') // ffl e -> ffle
    // Fix common spacing issues - more comprehensive patterns
    .replace(/\ba([a-z]{2,})/g, 'a $1')  // atear -> a tear, asecond -> a second, etc.
    .replace(/\bas([a-z]{2,})/g, 'as $1') // asif -> as if, ashe -> as he
    .replace(/\bat([a-z]{2,})/g, 'at $1') // atthe -> at the, atthat -> at that
    .replace(/\ban([a-z]{2,})/g, 'an $1') // anemotional -> an emotional, anentire -> an entire
    .replace(/\bP\.M\./g, '8 P.M.')  // P.M. -> 8 P.M.
    .replace(/\b(\d+)\s*P\.M\./g, '$1 P.M.') // Fix any other time formats

  // 3. Fix separated single letters at word boundaries (but be more careful)
  reconstructed = reconstructed
    .replace(/\b([A-Z]) (?=[a-z]{2,})/g, '$1')  // M any -> Many (only if next part is 2+ chars)
    .replace(/\b([a-z]) (?=[a-z]{2,})/g, '$1')  // w ith -> with (only if next part is 2+ chars)

  // 4. Clean up excessive spacing while preserving HTML structure
  reconstructed = reconstructed
    .replace(/\s+/g, ' ')  // Normalize all spacing
    .trim()

  return reconstructed
}

/**
 * Convert PDF text to simple, clean HTML - just like the original PDF
 */
function convertPDFTextToHTML(text: string): string {
  // Debug: Show what we're starting with
  console.log('BEFORE reconstruction:', text.substring(0, 200))

  // Universal PDF text reconstruction
  let cleanText = reconstructPDFText(text)

  // Debug: Show what we got after cleaning
  console.log('AFTER reconstruction:', cleanText.substring(0, 200))

  // Check if our fixes worked
  if (text.includes('<em>any </em>') && cleanText.includes('<em>Many </em>')) {
    console.log('✅ Successfully fixed "any" -> "Many"')
  } else if (text.includes('<em>any </em>')) {
    console.log('❌ Failed to fix "any" -> "Many"')
  }

  // Split into paragraphs at natural breaks (double spaces or line breaks)
  const paragraphs = cleanText
    .split(/\.\s+(?=[A-Z])/) // Split at sentence endings followed by capital letters
    .filter(para => para.trim().length > 10)

  let formattedHtml = ''
  let currentParagraph = ''

  for (let i = 0; i < paragraphs.length; i++) {
    let para = paragraphs[i].trim()

    // Add period back if it was removed by splitting
    if (!para.endsWith('.') && !para.endsWith('!') && !para.endsWith('?')) {
      para += '.'
    }

    currentParagraph += (currentParagraph ? ' ' : '') + para

    // Create paragraph every 2-3 sentences or when it gets long
    if (i % 3 === 2 || currentParagraph.length > 300 || i === paragraphs.length - 1) {
      if (currentParagraph.trim().length > 15) {
        formattedHtml += `<p>${currentParagraph.trim()}</p>\n\n`
      }
      currentParagraph = ''
    }
  }

  return formattedHtml.trim()
}

/**
 * Detect if a paragraph is likely a heading
 */
function isHeading(text: string): boolean {
  const trimmed = text.trim()

  // Check for explicit chapter markers (but these should already be handled by chapter extraction)
  if (/^(Chapter|CHAPTER)\s+\d+/i.test(trimmed)) {
    return true
  }

  // Don't treat long text as headings - this was causing the issue
  if (trimmed.length > 100) {
    return false
  }

  // Don't treat text that contains common sentence patterns as headings
  if (trimmed.includes('.') || trimmed.includes(',') || trimmed.includes(';')) {
    return false
  }

  // Don't treat text that starts with lowercase as headings
  if (!/^[A-Z]/.test(trimmed)) {
    return false
  }

  // Check for short ALL CAPS that look like headings (very conservative)
  if (trimmed === trimmed.toUpperCase() &&
      trimmed.length < 30 &&
      trimmed.length > 3 &&
      !/\s(the|and|of|to|in|for|with|on|at|by|from|any|people|fall|grace|because|most|unstable|destination|ever|exist|remains|popular|vacation|resort|however|unlimited|money|power|success|ability|whatever|want|life|answer|anybody|being|your|boss|basically|printing|living|terms|controlled|number|reality|still|alive|have|burden)\s/i.test(trimmed)) {
    return true
  }

  // Check for lines ending with colons (section headers) - very short only
  if (trimmed.endsWith(':') && trimmed.length < 30 && !trimmed.includes(',')) {
    return true
  }

  return false
}

/**
 * Determine heading level based on content
 */
function getHeadingLevel(text: string): number {
  if (/^(Chapter|CHAPTER)\s+/i.test(text)) return 1
  if (/^(Part|PART)\s+/i.test(text)) return 2
  if (/^(Section|SECTION)\s+/i.test(text)) return 3
  if (text === text.toUpperCase()) return 2
  return 3
}

/**
 * Detect if text contains a list
 */
function isList(text: string): boolean {
  const lines = text.split('\n').filter(line => line.trim())
  if (lines.length < 2) return false

  let listItems = 0
  for (const line of lines) {
    if (/^\s*[\-\•\*]/.test(line) || /^\s*\d+[\.\)]/.test(line)) {
      listItems++
    }
  }

  return listItems >= 2 && listItems / lines.length > 0.5
}

/**
 * Detect if text is a blockquote
 */
function isBlockquote(text: string): boolean {
  // Text surrounded by quotes
  if ((text.startsWith('"') && text.endsWith('"')) ||
      (text.startsWith("'") && text.endsWith("'"))) {
    return true
  }

  // Heavily indented text
  const lines = text.split('\n')
  let indentedLines = 0
  for (const line of lines) {
    if (/^\s{4,}/.test(line)) {
      indentedLines++
    }
  }

  return indentedLines / lines.length > 0.7
}

/**
 * Add emphasis to text (bold, italic) - more conservative approach
 */
function addEmphasisToText(text: string): string {
  return text
    // Convert *word* to <em>word</em>
    .replace(/\*([^*]+)\*/g, '<em>$1</em>')
    // Convert _word_ to <em>word</em>
    .replace(/_([^_]+)_/g, '<em>$1</em>')
    // Convert **word** to <strong>word</strong>
    .replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>')
    // Convert __word__ to <strong>word</strong>
    .replace(/__([^_]+)__/g, '<strong>$1</strong>')
    // Be very conservative with ALL CAPS - only emphasize obvious emphasis words
    .replace(/\b([A-Z]{3,8})\b/g, (match) => {
      // Only emphasize if it looks like intentional emphasis, not common words
      const commonWords = ['THE', 'AND', 'FOR', 'ARE', 'BUT', 'NOT', 'YOU', 'ALL', 'CAN', 'HER', 'WAS', 'ONE', 'OUR', 'HAD', 'HAVE', 'THIS', 'WILL', 'FROM', 'THEY', 'KNOW', 'WANT', 'BEEN', 'GOOD', 'MUCH', 'SOME', 'TIME', 'VERY', 'WHEN', 'COME', 'HERE', 'HOW', 'JUST', 'LIKE', 'LONG', 'MAKE', 'MANY', 'OVER', 'SUCH', 'TAKE', 'THAN', 'THEM', 'WELL', 'WERE', 'WITH', 'WHAT', 'WOULD', 'THERE', 'COULD', 'OTHER', 'AFTER', 'FIRST', 'NEVER', 'THESE', 'THINK', 'WHERE', 'BEING', 'EVERY', 'GREAT', 'MIGHT', 'SHALL', 'STILL', 'THOSE', 'UNDER', 'WHILE']

      if (commonWords.includes(match)) {
        return match
      }

      // Only emphasize if it's likely intentional emphasis
      if (match.length >= 4 && match.length <= 8) {
        return `<strong>${match}</strong>`
      }

      return match
    })
}

/**
 * Preserve EPUB formatting exactly as the author designed it
 */
function preserveEpubFormatting(html: string, chapterTitle?: string): string {
  // Keep original HTML structure but clean minimally for display
  let cleanHtml = html
    // Remove XML declarations and DOCTYPE
    .replace(/<\?xml[^>]*>/gi, '')
    .replace(/<!DOCTYPE[^>]*>/gi, '')

    // Remove head section but keep body content
    .replace(/<head[^>]*>[\s\S]*?<\/head>/gi, '')
    .replace(/<html[^>]*>/gi, '')
    .replace(/<\/html>/gi, '')
    .replace(/<body[^>]*>/gi, '')
    .replace(/<\/body>/gi, '')

    // Remove redundant chapter headings if we already have a title
    if (chapterTitle) {
      // Remove standalone chapter numbers and titles that duplicate our extracted title
      cleanHtml = cleanHtml
        .replace(/<h[1-6][^>]*>.*?<\/h[1-6]>/gi, '') // Remove all heading tags
        .replace(/<div[^>]*class="[^"]*heading[^"]*"[^>]*>[\s\S]*?<\/div>/gi, '') // Remove heading divs
        .replace(/<p[^>]*>\s*\d+\s*<\/p>/gi, '') // Remove standalone chapter numbers
        .replace(/<div[^>]*>\s*\d+\s*<\/div>/gi, '') // Remove div with just numbers
        .replace(/<p[^>]*>\s*(Chapter\s*\d+|CHAPTER\s*\d+)\s*<\/p>/gi, '') // Remove "Chapter X" paragraphs
    }

    // Decode essential HTML entities only
    cleanHtml = cleanHtml
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/&apos;/g, "'")
      .replace(/&lsquo;/g, "'")
      .replace(/&rsquo;/g, "'")
      .replace(/&ldquo;/g, '"')
      .replace(/&rdquo;/g, '"')
      .replace(/&mdash;/g, '—')
      .replace(/&ndash;/g, '–')
      .replace(/&hellip;/g, '…')

      // Minimal whitespace cleanup
      .replace(/^\s+|\s+$/g, '') // Trim start/end only
      .trim()

  return cleanHtml
}

// countWords function removed - using tokenizer directly

/**
 * Calculate readability score
 */
function calculateReadabilityScore(text: string): number {
  try {
    const sentences = sentenceTokenizer.tokenize(text)
    const words = wordTokenizer.tokenize(text)
    
    if (!sentences || !words || sentences.length === 0 || words.length === 0) {
      return 0
    }

    // Use Flesch Reading Ease score
    const score = readabilityScore({
      sentence: sentences.length,
      word: words.length,
      syllable: estimateSyllables(words)
    })

    return Math.round(score.fleschReadingEase || 0)
  } catch (error) {
    console.warn('Error calculating readability score:', error)
    return 0
  }
}

/**
 * Estimate syllables in words
 */
function estimateSyllables(words: string[]): number {
  return words.reduce((total, word) => {
    // Simple syllable estimation
    const vowels = word.match(/[aeiouy]+/gi)
    const syllableCount = vowels ? vowels.length : 1
    return total + Math.max(1, syllableCount)
  }, 0)
}

/**
 * Extract keywords and suggested tags with enhanced analysis
 */
function extractKeywords(text: string, existingSubjects?: string[], wordTokenizer?: any, sentenceTokenizer?: any): string[] {
  if (!wordTokenizer) return []
  const words = wordTokenizer.tokenize(text.toLowerCase())
  if (!words) return []

  // Enhanced stop words list
  const stopWords = new Set([
    'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them', 'my', 'your', 'his', 'her', 'its', 'our', 'their', 'said', 'say', 'says', 'go', 'goes', 'went', 'come', 'came', 'get', 'got', 'make', 'made', 'take', 'took', 'see', 'saw', 'know', 'knew', 'think', 'thought', 'look', 'looked', 'want', 'wanted', 'use', 'used', 'find', 'found', 'give', 'gave', 'tell', 'told', 'ask', 'asked', 'work', 'worked', 'seem', 'seemed', 'feel', 'felt', 'try', 'tried', 'leave', 'left', 'call', 'called', 'just', 'like', 'time', 'way', 'day', 'man', 'woman', 'people', 'thing', 'place', 'world', 'life', 'hand', 'part', 'child', 'eye', 'woman', 'place', 'work', 'week', 'case', 'point', 'government', 'company'
  ])

  // Genre-specific keywords that should be prioritized
  const genreKeywords = new Set([
    'love', 'romance', 'mystery', 'crime', 'detective', 'murder', 'fantasy', 'magic', 'dragon', 'wizard', 'science', 'fiction', 'space', 'alien', 'robot', 'future', 'horror', 'ghost', 'vampire', 'zombie', 'thriller', 'suspense', 'adventure', 'journey', 'quest', 'hero', 'villain', 'war', 'battle', 'soldier', 'historical', 'medieval', 'ancient', 'biography', 'memoir', 'autobiography', 'business', 'entrepreneur', 'success', 'leadership', 'psychology', 'philosophy', 'religion', 'spiritual', 'health', 'fitness', 'cooking', 'recipe', 'travel', 'guide', 'education', 'learning', 'children', 'family', 'parenting', 'relationship', 'marriage', 'friendship'
  ])

  // Count word frequency with genre weighting
  const wordCount = new Map<string, number>()
  words.forEach(word => {
    if (word.length > 3 && !stopWords.has(word) && /^[a-z]+$/.test(word)) {
      const weight = genreKeywords.has(word) ? 3 : 1 // Give genre keywords more weight
      wordCount.set(word, (wordCount.get(word) || 0) + weight)
    }
  })

  // Get most frequent words
  const sortedWords = Array.from(wordCount.entries())
    .sort((a, b) => b[1] - a[1])
    .slice(0, 15)
    .map(([word]) => word)

  // Smart genre detection based on content
  const detectedGenres = detectGenres(text, sortedWords)

  // Combine existing subjects, detected genres, and keywords
  const allTags = [...(existingSubjects || []), ...detectedGenres, ...sortedWords]

  // Remove duplicates and return top 10
  return Array.from(new Set(allTags)).slice(0, 10)
}

/**
 * Detect likely genres based on content analysis
 */
function detectGenres(text: string, keywords: string[]): string[] {
  const genres: string[] = []
  const lowerText = text.toLowerCase()

  // Romance indicators
  if (keywords.some(k => ['love', 'romance', 'heart', 'kiss', 'relationship', 'marriage', 'wedding'].includes(k)) ||
      /\b(love|romance|heart|kiss|relationship|marriage|wedding|passion|desire)\b/g.test(lowerText)) {
    genres.push('Romance')
  }

  // Mystery/Crime indicators
  if (keywords.some(k => ['mystery', 'crime', 'detective', 'murder', 'police', 'investigation'].includes(k)) ||
      /\b(mystery|crime|detective|murder|police|investigation|suspect|evidence|clue)\b/g.test(lowerText)) {
    genres.push('Mystery')
  }

  // Fantasy indicators
  if (keywords.some(k => ['fantasy', 'magic', 'dragon', 'wizard', 'spell', 'kingdom', 'quest'].includes(k)) ||
      /\b(magic|dragon|wizard|spell|kingdom|quest|fantasy|enchanted|mystical)\b/g.test(lowerText)) {
    genres.push('Fantasy')
  }

  // Science Fiction indicators
  if (keywords.some(k => ['science', 'fiction', 'space', 'alien', 'robot', 'future', 'technology'].includes(k)) ||
      /\b(space|alien|robot|future|technology|spacecraft|galaxy|planet|android)\b/g.test(lowerText)) {
    genres.push('Science Fiction')
  }

  // Horror indicators
  if (keywords.some(k => ['horror', 'ghost', 'vampire', 'zombie', 'demon', 'evil', 'dark'].includes(k)) ||
      /\b(horror|ghost|vampire|zombie|demon|evil|dark|terror|nightmare|haunted)\b/g.test(lowerText)) {
    genres.push('Horror')
  }

  // Thriller indicators
  if (keywords.some(k => ['thriller', 'suspense', 'danger', 'chase', 'escape', 'tension'].includes(k)) ||
      /\b(thriller|suspense|danger|chase|escape|tension|adrenaline|pursuit)\b/g.test(lowerText)) {
    genres.push('Thriller')
  }

  // Historical indicators
  if (keywords.some(k => ['historical', 'history', 'medieval', 'ancient', 'century', 'war'].includes(k)) ||
      /\b(historical|history|medieval|ancient|century|war|empire|dynasty|colonial)\b/g.test(lowerText)) {
    genres.push('Historical Fiction')
  }

  // Biography/Memoir indicators
  if (keywords.some(k => ['biography', 'memoir', 'autobiography', 'life', 'born', 'childhood'].includes(k)) ||
      /\b(biography|memoir|autobiography|born|childhood|grew up|my life|personal story)\b/g.test(lowerText)) {
    genres.push('Biography')
  }

  // Business indicators
  if (keywords.some(k => ['business', 'entrepreneur', 'success', 'leadership', 'management', 'strategy'].includes(k)) ||
      /\b(business|entrepreneur|success|leadership|management|strategy|corporate|profit)\b/g.test(lowerText)) {
    genres.push('Business')
  }

  // Self-Help indicators
  if (keywords.some(k => ['self', 'help', 'improvement', 'motivation', 'success', 'guide'].includes(k)) ||
      /\b(self-help|improvement|motivation|guide|how to|steps to|achieve|personal development)\b/g.test(lowerText)) {
    genres.push('Self-Help')
  }

  return genres
}

/**
 * Generate a compelling description from the text
 */
function generateDescription(text: string, sentenceTokenizer?: any): string {
  if (!sentenceTokenizer) return ''
  const sentences = sentenceTokenizer.tokenize(text)
  if (!sentences || sentences.length === 0) return ''

  // Look for compelling opening sentences (avoid common chapter headers)
  const filteredSentences = sentences.filter(sentence => {
    const lower = sentence.toLowerCase().trim()
    return lower.length > 20 &&
           !lower.startsWith('chapter') &&
           !lower.startsWith('part') &&
           !lower.startsWith('section') &&
           !lower.match(/^(table of contents|acknowledgments|dedication|preface|introduction)/)
  })

  // Take first few compelling sentences, up to 300 characters
  let description = ''
  for (const sentence of filteredSentences.slice(0, 4)) {
    if (description.length + sentence.length > 280) break
    description += sentence.trim() + ' '
  }

  // If description is too short, add more content
  if (description.length < 100 && filteredSentences.length > 4) {
    for (const sentence of filteredSentences.slice(4, 8)) {
      if (description.length + sentence.length > 280) break
      description += sentence.trim() + ' '
    }
  }

  return description.trim()
}

/**
 * Analyze content quality and provide insights
 */
export function analyzeContentQuality(text: string, wordTokenizer?: any, sentenceTokenizer?: any): {
  complexity: 'Simple' | 'Moderate' | 'Complex'
  sentiment: 'Positive' | 'Neutral' | 'Negative'
  pacing: 'Fast' | 'Moderate' | 'Slow'
  dialogueRatio: number
  descriptiveRatio: number
  actionRatio: number
} {
  if (!sentenceTokenizer || !wordTokenizer) {
    return {
      complexity: 'Moderate',
      sentiment: 'Neutral',
      pacing: 'Moderate',
      dialogueRatio: 0,
      descriptiveRatio: 0,
      actionRatio: 0
    }
  }

  const sentences = sentenceTokenizer.tokenize(text)
  const words = wordTokenizer.tokenize(text.toLowerCase())

  if (!sentences || !words) {
    return {
      complexity: 'Moderate',
      sentiment: 'Neutral',
      pacing: 'Moderate',
      dialogueRatio: 0,
      descriptiveRatio: 0,
      actionRatio: 0
    }
  }

  // Analyze sentence complexity
  const avgWordsPerSentence = words.length / sentences.length
  const complexity = avgWordsPerSentence > 20 ? 'Complex' : avgWordsPerSentence > 12 ? 'Moderate' : 'Simple'

  // Basic sentiment analysis
  const positiveWords = ['good', 'great', 'excellent', 'wonderful', 'amazing', 'beautiful', 'love', 'happy', 'joy', 'success', 'win', 'victory', 'hope', 'bright', 'smile', 'laugh']
  const negativeWords = ['bad', 'terrible', 'awful', 'horrible', 'hate', 'sad', 'angry', 'fear', 'death', 'pain', 'suffer', 'lose', 'fail', 'dark', 'cry', 'scream']

  const positiveCount = words.filter(word => positiveWords.includes(word)).length
  const negativeCount = words.filter(word => negativeWords.includes(word)).length

  const sentiment = positiveCount > negativeCount * 1.5 ? 'Positive' :
                   negativeCount > positiveCount * 1.5 ? 'Negative' : 'Neutral'

  // Analyze pacing through sentence length variation
  const shortSentences = sentences.filter(s => s.split(' ').length < 8).length
  const longSentences = sentences.filter(s => s.split(' ').length > 20).length
  const shortRatio = shortSentences / sentences.length

  const pacing = shortRatio > 0.4 ? 'Fast' : shortRatio < 0.2 ? 'Slow' : 'Moderate'

  // Analyze content types
  const dialogueMarkers = text.match(/["']/g)?.length || 0
  const dialogueRatio = Math.min(dialogueMarkers / (words.length / 100), 100) // Normalize to percentage

  const descriptiveWords = ['beautiful', 'dark', 'bright', 'large', 'small', 'old', 'new', 'red', 'blue', 'green', 'tall', 'short', 'wide', 'narrow', 'deep', 'shallow', 'rough', 'smooth', 'soft', 'hard']
  const descriptiveCount = words.filter(word => descriptiveWords.includes(word)).length
  const descriptiveRatio = (descriptiveCount / words.length) * 100

  const actionWords = ['run', 'jump', 'fight', 'chase', 'escape', 'attack', 'defend', 'move', 'rush', 'dash', 'leap', 'strike', 'hit', 'kick', 'punch', 'shoot', 'throw', 'catch']
  const actionCount = words.filter(word => actionWords.includes(word)).length
  const actionRatio = (actionCount / words.length) * 100

  return {
    complexity,
    sentiment,
    pacing,
    dialogueRatio: Math.round(dialogueRatio),
    descriptiveRatio: Math.round(descriptiveRatio),
    actionRatio: Math.round(actionRatio)
  }
}
